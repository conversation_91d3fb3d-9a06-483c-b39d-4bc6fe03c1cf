/**
 * 测试 applyConfigMapping 函数
 */

// 简单的测试，直接调用模块
const ConfigEngine = require('./src/main/engine/config-engine');

// 复制检测函数
function detectSubscriptionConfig(config) {
  if (!config || typeof config !== 'object') {
    return false;
  }
  const hasOutbounds = Array.isArray(config.outbounds) && config.outbounds.length > 0;
  const hasProxies = Array.isArray(config.proxies) && config.proxies.length > 0;
  const hasRoute = config.route && (config.route.rules || config.route.rule_set);
  const hasDNS = config.dns && (config.dns.servers || config.dns.rules);
  const subscriptionFeatures = [hasOutbounds, hasProxies, hasRoute, hasDNS].filter(Boolean).length;
  return subscriptionFeatures >= 2;
}

// 复制映射函数的核心逻辑
async function testApplyConfigMapping(userConfig, targetConfig) {
  const engine = new ConfigEngine({
    enableCache: true,
    enableValidation: true,
    enableBackup: true
  });

  if (targetConfig && Object.keys(targetConfig).length > 0) {
    await engine.injectionAPI.loadConfigFromObject(targetConfig);
  }

  const isSubscriptionConfig = detectSubscriptionConfig(targetConfig);
  const mappingOptions = {
    restrictedMode: isSubscriptionConfig
  };

  console.log(`检测到${isSubscriptionConfig ? '订阅' : '用户'}配置文件，${isSubscriptionConfig ? '启用受限映射模式' : '使用完整映射模式'}`);

  const result = await engine.applyUserConfigMapping(userConfig, mappingOptions);
  return result;
}

async function runTest() {
  console.log('=== 测试 applyConfigMapping 函数 ===\n');

  const userConfig = {
    settings: {
      allow_lan: true,
      log_level: 'debug',
      tun_mode: true
    },
    proxy: {
      mixed: {
        enabled: true,
        port: 7890
      }
    }
  };

  const subscriptionConfig = {
    outbounds: [
      { 
        type: 'shadowsocks', 
        tag: 'proxy1', 
        server: '*******', 
        server_port: 443,
        method: 'aes-256-gcm',
        password: 'test123'
      },
      { type: 'direct', tag: 'direct' }
    ],
    route: {
      rules: [
        { domain: ['google.com'], outbound: 'proxy1' }
      ],
      final: 'proxy1'
    },
    dns: {
      servers: [
        { tag: 'remote', address: '*******' }
      ],
      final: 'remote'
    }
  };

  // 1. 测试订阅配置检测
  console.log('1. 测试订阅配置检测:');
  const isSubscription = detectSubscriptionConfig(subscriptionConfig);
  console.log(`  检测结果: ${isSubscription ? '✓ 订阅配置' : '✗ 非订阅配置'}`);

  // 2. 测试配置映射
  console.log('\n2. 测试配置映射:');
  try {
    const result = await testApplyConfigMapping(userConfig, subscriptionConfig);

    if (result.success) {
      const config = result.config;
      console.log('  映射结果:');
      console.log(`    - 出站数量: ${config.outbounds?.length || 0}`);
      console.log(`    - 入站数量: ${config.inbounds?.length || 0}`);
      console.log(`    - DNS服务器数量: ${config.dns?.servers?.length || 0}`);
      console.log(`    - 路由规则数量: ${config.route?.rules?.length || 0}`);
      console.log(`    - 日志级别: ${config.log?.level || '无'}`);

      if (config.inbounds) {
        console.log('  入站详情:');
        config.inbounds.forEach((inbound, index) => {
          console.log(`    ${index + 1}. ${inbound.type} (${inbound.tag || '无标签'})`);
        });
      }

      // 验证保护效果
      const dnsPreserved = JSON.stringify(config.dns) === JSON.stringify(subscriptionConfig.dns);
      const routePreserved = JSON.stringify(config.route) === JSON.stringify(subscriptionConfig.route);

      console.log('\n  保护效果:');
      console.log(`    - DNS配置保护: ${dnsPreserved ? '✓' : '✗'}`);
      console.log(`    - 路由配置保护: ${routePreserved ? '✓' : '✗'}`);

      // 验证必要设置注入
      const hasLogConfig = config.log && config.log.level === 'debug';
      const hasInbounds = config.inbounds && config.inbounds.length > 0;

      console.log('\n  设置注入:');
      console.log(`    - 日志配置: ${hasLogConfig ? '✓' : '✗'}`);
      console.log(`    - 入站配置: ${hasInbounds ? '✓' : '✗'}`);
    } else {
      console.error(`  映射失败: ${result.error}`);
    }

  } catch (error) {
    console.error(`  映射失败: ${error.message}`);
    console.error(error.stack);
  }

  console.log('\n=== 测试完成 ===');
}

runTest().catch(console.error);
