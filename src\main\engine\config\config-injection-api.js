/**
 * 配置注入API
 * 提供清晰的配置读取、注入、更新和回滚功能
 */

const fs = require('fs').promises;
const path = require('path');
const logger = require('../../../utils/logger');
const SingBoxConfigParser = require('./sing-box-parser');
const ConfigManipulator = require('./config-manipulator');
const ConfigValidator = require('./config-validator');
const MappingEngine = require('../mapping/mapping-engine');

/**
 * 配置注入API类
 */
class ConfigInjectionAPI {
  constructor(options = {}) {
    this.parser = new SingBoxConfigParser();
    this.manipulator = new ConfigManipulator();
    this.validator = new ConfigValidator();
    this.mappingEngine = new MappingEngine(options.mappingEngine || {});
    this.configHistory = [];
    this.maxHistorySize = 10;
    this.currentConfigPath = null;
    this.currentConfig = null;
    this.backupConfigs = new Map();
    this.options = {
      defaultRestrictedMode: false, // 默认是否使用受限模式
      ...options
    };
  }

  /**
   * 加载配置文件
   * @param {string} configPath 配置文件路径
   * @returns {Promise<Object>} 加载结果
   */
  async loadConfig(configPath) {
    try {
      const result = await this.parser.parseConfigFile(configPath);
      
      if (!result.success) {
        throw new Error(`配置文件解析失败: ${result.error}`);
      }

      // 验证配置
      const validation = this.validator.validateConfig(result.config);
      if (!validation.valid) {
        logger.warn(`配置验证警告: ${validation.errors.join(', ')}`);
      }

      this.currentConfigPath = configPath;
      this.currentConfig = result.config;
      
      // 创建备份
      await this.createBackup('load');

      logger.info(`配置文件加载成功: ${configPath}`);
      
      return {
        success: true,
        config: result.config,
        analysis: result.analysis,
        validation
      };
    } catch (error) {
      logger.error(`加载配置文件失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 保存配置文件
   * @param {string} configPath 配置文件路径（可选）
   * @param {Object} config 配置对象（可选）
   * @returns {Promise<Object>} 保存结果
   */
  async saveConfig(configPath = null, config = null) {
    try {
      const targetPath = configPath || this.currentConfigPath;
      const targetConfig = config || this.currentConfig;

      if (!targetPath) {
        throw new Error('未指定配置文件路径');
      }

      if (!targetConfig) {
        throw new Error('没有可保存的配置');
      }

      // 验证配置
      const validation = this.validator.validateConfig(targetConfig);
      if (!validation.valid) {
        throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
      }

      // 格式化JSON
      const configContent = JSON.stringify(targetConfig, null, 2);
      
      // 创建备份
      if (await this.fileExists(targetPath)) {
        await this.createBackup('save');
      }

      // 保存文件
      await fs.writeFile(targetPath, configContent, 'utf8');
      
      this.currentConfigPath = targetPath;
      this.currentConfig = targetConfig;

      logger.info(`配置文件保存成功: ${targetPath}`);
      
      return {
        success: true,
        path: targetPath,
        validation
      };
    } catch (error) {
      logger.error(`保存配置文件失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从配置对象加载配置
   * @param {Object} configObject 配置对象
   * @returns {Promise<Object>} 加载结果
   */
  async loadConfigFromObject(configObject) {
    try {
      // 验证配置对象
      const validation = this.validator.validateConfig(configObject);

      if (!validation.valid) {
        logger.warn(`配置验证警告: ${validation.errors.join(', ')}`);
      }

      this.currentConfig = JSON.parse(JSON.stringify(configObject)); // 深拷贝
      this.currentConfigPath = null; // 标记为内存配置

      // 创建备份
      await this.createBackup('load_object');

      logger.info('配置对象加载成功');

      return {
        success: true,
        config: this.currentConfig,
        validation
      };
    } catch (error) {
      logger.error(`加载配置对象失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 应用用户配置映射
   * @param {Object} userConfig 用户配置
   * @param {Object} options 选项
   * @returns {Promise<Object>} 应用结果
   */
  async applyUserConfig(userConfig, options = {}) {
    try {
      const {
        merge = true,
        validate = true,
        backup = true,
        restrictedMode = this.options.defaultRestrictedMode
      } = options;

      // 创建备份
      if (backup) {
        await this.createBackup('apply_user_config');
      }

      // 获取基础配置
      const baseConfig = merge && this.currentConfig ? this.currentConfig : {};

      // 根据受限模式选择映射引擎
      let mappingEngine = this.mappingEngine;
      if (restrictedMode !== this.mappingEngine.options.restrictedMode) {
        // 创建临时映射引擎使用不同的模式
        const MappingEngineClass = require('../mapping/mapping-engine');
        mappingEngine = new MappingEngineClass({ restrictedMode });
      }

      // 应用映射
      const mappingResult = await mappingEngine.applyMappings(userConfig, baseConfig);
      
      if (!mappingResult.success) {
        throw new Error(`映射应用失败: ${mappingResult.error}`);
      }

      const newConfig = mappingResult.config;

      // 验证配置
      if (validate) {
        const validation = this.validator.validateConfig(newConfig);
        if (!validation.valid) {
          throw new Error(`生成的配置验证失败: ${validation.errors.join(', ')}`);
        }
      }

      this.currentConfig = newConfig;

      logger.info('用户配置映射应用成功');
      
      return {
        success: true,
        config: newConfig,
        statistics: mappingResult.statistics
      };
    } catch (error) {
      logger.error(`应用用户配置失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 增量更新配置
   * @param {Object} updates 更新内容
   * @param {Object} options 选项
   * @returns {Promise<Object>} 更新结果
   */
  async updateConfig(updates, options = {}) {
    try {
      const {
        validate = true,
        backup = true,
        conflictStrategy = 'merge' // 'merge', 'replace', 'skip'
      } = options;

      if (!this.currentConfig) {
        throw new Error('没有当前配置可更新');
      }

      // 创建备份
      if (backup) {
        await this.createBackup('update');
      }

      let newConfig = this.manipulator.cloneConfig(this.currentConfig);

      // 应用更新
      Object.entries(updates).forEach(([section, sectionUpdates]) => {
        switch (section) {
          case 'inbounds':
            newConfig = this.updateInbounds(newConfig, sectionUpdates, conflictStrategy);
            break;
          case 'outbounds':
            newConfig = this.updateOutbounds(newConfig, sectionUpdates, conflictStrategy);
            break;
          case 'route':
            newConfig = this.manipulator.updateRoute(newConfig, sectionUpdates);
            break;
          case 'dns':
            newConfig = this.manipulator.updateDNS(newConfig, sectionUpdates);
            break;
          case 'log':
            newConfig = this.manipulator.updateLog(newConfig, sectionUpdates);
            break;
          case 'experimental':
            newConfig = this.manipulator.updateExperimental(newConfig, sectionUpdates);
            break;
          default:
            // 直接设置其他字段
            newConfig[section] = sectionUpdates;
        }
      });

      // 验证配置
      if (validate) {
        const validation = this.validator.validateConfig(newConfig);
        if (!validation.valid) {
          throw new Error(`更新后的配置验证失败: ${validation.errors.join(', ')}`);
        }
      }

      this.currentConfig = newConfig;

      logger.info('配置增量更新成功');
      
      return {
        success: true,
        config: newConfig,
        changes: this.manipulator.getChangeHistory()
      };
    } catch (error) {
      logger.error(`配置更新失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新入站配置
   * @param {Object} config 配置对象
   * @param {Array|Object} updates 更新内容
   * @param {string} conflictStrategy 冲突策略
   * @returns {Object} 更新后的配置
   */
  updateInbounds(config, updates, conflictStrategy) {
    let newConfig = config;

    if (Array.isArray(updates)) {
      // 批量更新
      updates.forEach(update => {
        if (update.action === 'add') {
          newConfig = this.manipulator.addInbound(newConfig, update.inbound);
        } else if (update.action === 'update' && update.tag) {
          newConfig = this.manipulator.updateInbound(newConfig, update.tag, update.updates);
        } else if (update.action === 'remove' && update.tag) {
          newConfig = this.manipulator.removeInbound(newConfig, update.tag);
        }
      });
    } else if (typeof updates === 'object') {
      // 对象形式的更新
      Object.entries(updates).forEach(([tag, inboundUpdates]) => {
        try {
          newConfig = this.manipulator.updateInbound(newConfig, tag, inboundUpdates);
        } catch (error) {
          if (conflictStrategy === 'skip') {
            logger.warn(`跳过入站 ${tag} 的更新: ${error.message}`);
          } else {
            throw error;
          }
        }
      });
    }

    return newConfig;
  }

  /**
   * 更新出站配置
   * @param {Object} config 配置对象
   * @param {Array|Object} updates 更新内容
   * @param {string} conflictStrategy 冲突策略
   * @returns {Object} 更新后的配置
   */
  updateOutbounds(config, updates, conflictStrategy) {
    let newConfig = config;

    if (Array.isArray(updates)) {
      // 批量更新
      updates.forEach(update => {
        if (update.action === 'add') {
          newConfig = this.manipulator.addOutbound(newConfig, update.outbound);
        } else if (update.action === 'update' && update.tag) {
          newConfig = this.manipulator.updateOutbound(newConfig, update.tag, update.updates);
        } else if (update.action === 'remove' && update.tag) {
          newConfig = this.manipulator.removeOutbound(newConfig, update.tag);
        }
      });
    } else if (typeof updates === 'object') {
      // 对象形式的更新
      Object.entries(updates).forEach(([tag, outboundUpdates]) => {
        try {
          newConfig = this.manipulator.updateOutbound(newConfig, tag, outboundUpdates);
        } catch (error) {
          if (conflictStrategy === 'skip') {
            logger.warn(`跳过出站 ${tag} 的更新: ${error.message}`);
          } else {
            throw error;
          }
        }
      });
    }

    return newConfig;
  }

  /**
   * 回滚配置
   * @param {string} backupId 备份ID（可选）
   * @returns {Promise<Object>} 回滚结果
   */
  async rollbackConfig(backupId = null) {
    try {
      let backupConfig;

      if (backupId) {
        if (!this.backupConfigs.has(backupId)) {
          throw new Error(`找不到备份: ${backupId}`);
        }
        backupConfig = this.backupConfigs.get(backupId);
      } else {
        // 使用最新的备份
        const backupIds = Array.from(this.backupConfigs.keys()).sort().reverse();
        if (backupIds.length === 0) {
          throw new Error('没有可用的备份');
        }
        backupConfig = this.backupConfigs.get(backupIds[0]);
      }

      this.currentConfig = this.manipulator.cloneConfig(backupConfig.config);

      logger.info(`配置回滚成功: ${backupConfig.reason}`);
      
      return {
        success: true,
        config: this.currentConfig,
        backup: backupConfig
      };
    } catch (error) {
      logger.error(`配置回滚失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建配置备份
   * @param {string} reason 备份原因
   * @returns {Promise<string>} 备份ID
   */
  async createBackup(reason = 'manual') {
    if (!this.currentConfig) {
      throw new Error('没有当前配置可备份');
    }

    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const backup = {
      id: backupId,
      timestamp: new Date().toISOString(),
      reason,
      config: this.manipulator.cloneConfig(this.currentConfig),
      path: this.currentConfigPath
    };

    this.backupConfigs.set(backupId, backup);

    // 限制备份数量
    if (this.backupConfigs.size > this.maxHistorySize) {
      const oldestId = Array.from(this.backupConfigs.keys()).sort()[0];
      this.backupConfigs.delete(oldestId);
    }

    logger.debug(`创建配置备份: ${backupId} (${reason})`);
    
    return backupId;
  }

  /**
   * 获取备份列表
   * @returns {Array} 备份列表
   */
  getBackupList() {
    return Array.from(this.backupConfigs.values()).sort((a, b) => 
      new Date(b.timestamp) - new Date(a.timestamp)
    );
  }

  /**
   * 删除备份
   * @param {string} backupId 备份ID
   * @returns {boolean} 删除结果
   */
  deleteBackup(backupId) {
    return this.backupConfigs.delete(backupId);
  }

  /**
   * 比较配置差异
   * @param {Object} config1 配置1
   * @param {Object} config2 配置2
   * @returns {Object} 差异报告
   */
  compareConfigs(config1, config2) {
    return this.manipulator.compareConfigs(config1, config2);
  }

  /**
   * 获取当前配置
   * @returns {Object} 当前配置
   */
  getCurrentConfig() {
    return this.currentConfig ? this.manipulator.cloneConfig(this.currentConfig) : null;
  }

  /**
   * 获取配置分析
   * @param {Object} config 配置对象（可选）
   * @returns {Object} 配置分析
   */
  getConfigAnalysis(config = null) {
    const targetConfig = config || this.currentConfig;
    if (!targetConfig) {
      throw new Error('没有可分析的配置');
    }

    return this.parser.analyzeConfig(targetConfig);
  }

  /**
   * 验证配置
   * @param {Object} config 配置对象（可选）
   * @returns {Object} 验证结果
   */
  validateConfig(config = null) {
    const targetConfig = config || this.currentConfig;
    if (!targetConfig) {
      throw new Error('没有可验证的配置');
    }

    return this.validator.validateConfig(targetConfig);
  }

  /**
   * 检查文件是否存在
   * @param {string} filePath 文件路径
   * @returns {Promise<boolean>} 文件是否存在
   */
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.backupConfigs.clear();
    this.manipulator.clearHistory();
    this.mappingEngine.clearCache();
    this.currentConfig = null;
    this.currentConfigPath = null;
  }
}

module.exports = ConfigInjectionAPI;
