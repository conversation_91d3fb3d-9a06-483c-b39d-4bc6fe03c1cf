{"version": 3, "names": ["_template", "require", "_t", "blockStatement", "callExpression", "functionExpression", "isAssignmentPattern", "isFunctionDeclaration", "isRestElement", "returnStatement", "isCallExpression", "memberExpression", "identifier", "thisExpression", "isPattern", "buildAnonymousExpressionWrapper", "template", "expression", "buildNamedExpressionWrapper", "buildDeclarationWrapper", "statements", "classOrObjectMethod", "path", "callId", "ignoreFunctionLength", "node", "body", "params", "shoudlForwardParams", "some", "p", "param", "push", "scope", "generateUidIdentifier", "container", "get", "unwrapFunctionEnvironment", "async", "generator", "plainFunction", "inPath", "noNewArrows", "<PERSON><PERSON><PERSON>", "functionId", "nodeParams", "isArrowFunctionExpression", "_path$arrowFunctionTo", "arrowFunctionToExpression", "isDeclaration", "built", "id", "type", "wrapperArgs", "NAME", "REF", "name", "FUNCTION", "PARAMS", "replaceWith", "insertAfter", "length", "wrapFunction", "isMethod", "_path", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport template from \"@babel/template\";\nimport {\n  blockStatement,\n  callExpression,\n  functionExpression,\n  isAssignmentPattern,\n  isFunctionDeclaration,\n  isRestElement,\n  returnStatement,\n  isCallExpression,\n  memberExpression,\n  identifier,\n  thisExpression,\n  isPattern,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\ntype ExpressionWrapperBuilder<ExtraBody extends t.Node[]> = (\n  replacements?: Parameters<ReturnType<typeof template.expression>>[0],\n) => t.CallExpression & {\n  callee: t.FunctionExpression & {\n    body: {\n      body: [\n        t.VariableDeclaration & {\n          declarations: [\n            { init: t.FunctionExpression | t.ArrowFunctionExpression },\n          ];\n        },\n        ...ExtraBody,\n      ];\n    };\n  };\n};\n\nconst buildAnonymousExpressionWrapper = template.expression(`\n  (function () {\n    var REF = FUNCTION;\n    return function NAME(PARAMS) {\n      return REF.apply(this, arguments);\n    };\n  })()\n`) as ExpressionWrapperBuilder<\n  [t.ReturnStatement & { argument: t.FunctionExpression }]\n>;\n\nconst buildNamedExpressionWrapper = template.expression(`\n  (function () {\n    var REF = FUNCTION;\n    function NAME(PARAMS) {\n      return REF.apply(this, arguments);\n    }\n    return NAME;\n  })()\n`) as ExpressionWrapperBuilder<\n  [t.FunctionDeclaration, t.ReturnStatement & { argument: t.Identifier }]\n>;\n\nconst buildDeclarationWrapper = template.statements(`\n  function NAME(PARAMS) { return REF.apply(this, arguments); }\n  function REF() {\n    REF = FUNCTION;\n    return REF.apply(this, arguments);\n  }\n`);\n\nfunction classOrObjectMethod(\n  path: NodePath<t.ClassMethod | t.ClassPrivateMethod | t.ObjectMethod>,\n  callId: t.Expression,\n  ignoreFunctionLength: boolean,\n) {\n  const node = path.node;\n  const body = node.body;\n\n  let params: Array<t.Identifier | t.Pattern | t.RestElement> = [];\n\n  // Errors thrown during argument evaluation must reject the resulting promise\n  const shoudlForwardParams = node.params.some(p => isPattern(p));\n\n  if (shoudlForwardParams) {\n    params = node.params as typeof params;\n    node.params = [];\n    if (!ignoreFunctionLength) {\n      for (const param of params) {\n        if (isAssignmentPattern(param) || isRestElement(param)) {\n          break;\n        }\n        node.params.push(path.scope.generateUidIdentifier(\"x\"));\n      }\n    }\n  }\n\n  const container = functionExpression(\n    null,\n    params,\n    blockStatement(body.body),\n    true,\n  );\n\n  if (shoudlForwardParams) {\n    // return asyncToGenerator(function*() { ... }).apply(this, arguments);\n    body.body = [\n      returnStatement(\n        callExpression(\n          memberExpression(\n            callExpression(callId, [container]),\n            identifier(\"apply\"),\n          ),\n          [thisExpression(), identifier(\"arguments\")],\n        ),\n      ),\n    ];\n\n    (\n      path.get(\"body.body.0.argument.callee.object.arguments.0\") as NodePath\n    ).unwrapFunctionEnvironment();\n  } else {\n    // return asyncToGenerator(function*() { ... })();\n    body.body = [\n      returnStatement(callExpression(callExpression(callId, [container]), [])),\n    ];\n\n    // Unwrap the wrapper IIFE's environment so super and this and such still work.\n    (\n      path.get(\"body.body.0.argument.callee.arguments.0\") as NodePath\n    ).unwrapFunctionEnvironment();\n  }\n\n  // Regardless of whether or not the wrapped function is a an async method\n  // or generator the outer function should not be\n  node.async = false;\n  node.generator = false;\n}\n\nfunction plainFunction(\n  inPath: NodePath<Exclude<t.Function, t.Method>>,\n  callId: t.Expression,\n  noNewArrows: boolean,\n  ignoreFunctionLength: boolean,\n  hadName: boolean,\n) {\n  let path: NodePath<\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.CallExpression\n    | t.ArrowFunctionExpression\n  > = inPath;\n  let node;\n  let functionId = null;\n  const nodeParams = inPath.node.params;\n\n  if (path.isArrowFunctionExpression()) {\n    if (process.env.BABEL_8_BREAKING) {\n      path = path.arrowFunctionToExpression({ noNewArrows });\n    } else {\n      // arrowFunctionToExpression returns undefined in @babel/traverse < 7.18.10\n      path = path.arrowFunctionToExpression({ noNewArrows }) ?? path;\n    }\n    node = path.node as\n      | t.FunctionDeclaration\n      | t.FunctionExpression\n      | t.CallExpression;\n  } else {\n    node = path.node;\n  }\n\n  const isDeclaration = isFunctionDeclaration(node);\n\n  let built = node;\n  if (!isCallExpression(node)) {\n    functionId = node.id;\n    node.id = null;\n    node.type = \"FunctionExpression\";\n    built = callExpression(callId, [\n      node as Exclude<typeof node, t.FunctionDeclaration>,\n    ]);\n  }\n\n  const params: t.Identifier[] = [];\n  for (const param of nodeParams) {\n    if (isAssignmentPattern(param) || isRestElement(param)) {\n      break;\n    }\n    params.push(path.scope.generateUidIdentifier(\"x\"));\n  }\n\n  const wrapperArgs = {\n    NAME: functionId || null,\n    // TODO: Use `functionId` rather than `hadName` for the condition\n    REF: path.scope.generateUidIdentifier(hadName ? functionId.name : \"ref\"),\n    FUNCTION: built,\n    PARAMS: params,\n  };\n\n  if (isDeclaration) {\n    const container = buildDeclarationWrapper(wrapperArgs);\n    path.replaceWith(container[0]);\n    path.insertAfter(container[1]);\n  } else {\n    let container;\n\n    if (hadName) {\n      container = buildNamedExpressionWrapper(wrapperArgs);\n    } else {\n      container = buildAnonymousExpressionWrapper(wrapperArgs);\n    }\n\n    if (functionId || (!ignoreFunctionLength && params.length)) {\n      path.replaceWith(container);\n    } else {\n      // we can omit this wrapper as the conditions it protects for do not apply\n      path.replaceWith(built);\n    }\n  }\n}\n\nexport default function wrapFunction(\n  path: NodePath<t.Function>,\n  callId: t.Expression,\n  // TODO(Babel 8): Consider defaulting to false for spec compliance\n  noNewArrows: boolean = true,\n  ignoreFunctionLength: boolean = false,\n) {\n  if (path.isMethod()) {\n    classOrObjectMethod(path, callId, ignoreFunctionLength);\n  } else {\n    const hadName = \"id\" in path.node && !!path.node.id;\n    if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n      // polyfill when being run by an older Babel version\n      path.ensureFunctionName ??=\n        // eslint-disable-next-line no-restricted-globals\n        require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n    }\n    // @ts-expect-error It is invalid to call this on an arrow expression,\n    // but we'll convert it to a function expression anyway.\n    path = path.ensureFunctionName(false);\n    plainFunction(\n      path as NodePath<Exclude<t.Function, t.Method>>,\n      callId,\n      noNewArrows,\n      ignoreFunctionLength,\n      hadName,\n    );\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,EAAA,GAAAD,OAAA;AAasB;EAZpBE,cAAc;EACdC,cAAc;EACdC,kBAAkB;EAClBC,mBAAmB;EACnBC,qBAAqB;EACrBC,aAAa;EACbC,eAAe;EACfC,gBAAgB;EAChBC,gBAAgB;EAChBC,UAAU;EACVC,cAAc;EACdC;AAAS,IAAAZ,EAAA;AAqBX,MAAMa,+BAA+B,GAAGC,iBAAQ,CAACC,UAAU,CAAC;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAEA;AAED,MAAMC,2BAA2B,GAAGF,iBAAQ,CAACC,UAAU,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,CAEA;AAED,MAAME,uBAAuB,GAAGH,iBAAQ,CAACI,UAAU,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA,CAAC,CAAC;AAEF,SAASC,mBAAmBA,CAC1BC,IAAqE,EACrEC,MAAoB,EACpBC,oBAA6B,EAC7B;EACA,MAAMC,IAAI,GAAGH,IAAI,CAACG,IAAI;EACtB,MAAMC,IAAI,GAAGD,IAAI,CAACC,IAAI;EAEtB,IAAIC,MAAuD,GAAG,EAAE;EAGhE,MAAMC,mBAAmB,GAAGH,IAAI,CAACE,MAAM,CAACE,IAAI,CAACC,CAAC,IAAIhB,SAAS,CAACgB,CAAC,CAAC,CAAC;EAE/D,IAAIF,mBAAmB,EAAE;IACvBD,MAAM,GAAGF,IAAI,CAACE,MAAuB;IACrCF,IAAI,CAACE,MAAM,GAAG,EAAE;IAChB,IAAI,CAACH,oBAAoB,EAAE;MACzB,KAAK,MAAMO,KAAK,IAAIJ,MAAM,EAAE;QAC1B,IAAIrB,mBAAmB,CAACyB,KAAK,CAAC,IAAIvB,aAAa,CAACuB,KAAK,CAAC,EAAE;UACtD;QACF;QACAN,IAAI,CAACE,MAAM,CAACK,IAAI,CAACV,IAAI,CAACW,KAAK,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAC;MACzD;IACF;EACF;EAEA,MAAMC,SAAS,GAAG9B,kBAAkB,CAClC,IAAI,EACJsB,MAAM,EACNxB,cAAc,CAACuB,IAAI,CAACA,IAAI,CAAC,EACzB,IACF,CAAC;EAED,IAAIE,mBAAmB,EAAE;IAEvBF,IAAI,CAACA,IAAI,GAAG,CACVjB,eAAe,CACbL,cAAc,CACZO,gBAAgB,CACdP,cAAc,CAACmB,MAAM,EAAE,CAACY,SAAS,CAAC,CAAC,EACnCvB,UAAU,CAAC,OAAO,CACpB,CAAC,EACD,CAACC,cAAc,CAAC,CAAC,EAAED,UAAU,CAAC,WAAW,CAAC,CAC5C,CACF,CAAC,CACF;IAGCU,IAAI,CAACc,GAAG,CAAC,gDAAgD,CAAC,CAC1DC,yBAAyB,CAAC,CAAC;EAC/B,CAAC,MAAM;IAELX,IAAI,CAACA,IAAI,GAAG,CACVjB,eAAe,CAACL,cAAc,CAACA,cAAc,CAACmB,MAAM,EAAE,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CACzE;IAICb,IAAI,CAACc,GAAG,CAAC,yCAAyC,CAAC,CACnDC,yBAAyB,CAAC,CAAC;EAC/B;EAIAZ,IAAI,CAACa,KAAK,GAAG,KAAK;EAClBb,IAAI,CAACc,SAAS,GAAG,KAAK;AACxB;AAEA,SAASC,aAAaA,CACpBC,MAA+C,EAC/ClB,MAAoB,EACpBmB,WAAoB,EACpBlB,oBAA6B,EAC7BmB,OAAgB,EAChB;EACA,IAAIrB,IAKH,GAAGmB,MAAM;EACV,IAAIhB,IAAI;EACR,IAAImB,UAAU,GAAG,IAAI;EACrB,MAAMC,UAAU,GAAGJ,MAAM,CAAChB,IAAI,CAACE,MAAM;EAErC,IAAIL,IAAI,CAACwB,yBAAyB,CAAC,CAAC,EAAE;IAG7B;MAAA,IAAAC,qBAAA;MAELzB,IAAI,IAAAyB,qBAAA,GAAGzB,IAAI,CAAC0B,yBAAyB,CAAC;QAAEN;MAAY,CAAC,CAAC,YAAAK,qBAAA,GAAIzB,IAAI;IAChE;IACAG,IAAI,GAAGH,IAAI,CAACG,IAGQ;EACtB,CAAC,MAAM;IACLA,IAAI,GAAGH,IAAI,CAACG,IAAI;EAClB;EAEA,MAAMwB,aAAa,GAAG1C,qBAAqB,CAACkB,IAAI,CAAC;EAEjD,IAAIyB,KAAK,GAAGzB,IAAI;EAChB,IAAI,CAACf,gBAAgB,CAACe,IAAI,CAAC,EAAE;IAC3BmB,UAAU,GAAGnB,IAAI,CAAC0B,EAAE;IACpB1B,IAAI,CAAC0B,EAAE,GAAG,IAAI;IACd1B,IAAI,CAAC2B,IAAI,GAAG,oBAAoB;IAChCF,KAAK,GAAG9C,cAAc,CAACmB,MAAM,EAAE,CAC7BE,IAAI,CACL,CAAC;EACJ;EAEA,MAAME,MAAsB,GAAG,EAAE;EACjC,KAAK,MAAMI,KAAK,IAAIc,UAAU,EAAE;IAC9B,IAAIvC,mBAAmB,CAACyB,KAAK,CAAC,IAAIvB,aAAa,CAACuB,KAAK,CAAC,EAAE;MACtD;IACF;IACAJ,MAAM,CAACK,IAAI,CAACV,IAAI,CAACW,KAAK,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA,MAAMmB,WAAW,GAAG;IAClBC,IAAI,EAAEV,UAAU,IAAI,IAAI;IAExBW,GAAG,EAAEjC,IAAI,CAACW,KAAK,CAACC,qBAAqB,CAACS,OAAO,GAAGC,UAAU,CAACY,IAAI,GAAG,KAAK,CAAC;IACxEC,QAAQ,EAAEP,KAAK;IACfQ,MAAM,EAAE/B;EACV,CAAC;EAED,IAAIsB,aAAa,EAAE;IACjB,MAAMd,SAAS,GAAGhB,uBAAuB,CAACkC,WAAW,CAAC;IACtD/B,IAAI,CAACqC,WAAW,CAACxB,SAAS,CAAC,CAAC,CAAC,CAAC;IAC9Bb,IAAI,CAACsC,WAAW,CAACzB,SAAS,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC,MAAM;IACL,IAAIA,SAAS;IAEb,IAAIQ,OAAO,EAAE;MACXR,SAAS,GAAGjB,2BAA2B,CAACmC,WAAW,CAAC;IACtD,CAAC,MAAM;MACLlB,SAAS,GAAGpB,+BAA+B,CAACsC,WAAW,CAAC;IAC1D;IAEA,IAAIT,UAAU,IAAK,CAACpB,oBAAoB,IAAIG,MAAM,CAACkC,MAAO,EAAE;MAC1DvC,IAAI,CAACqC,WAAW,CAACxB,SAAS,CAAC;IAC7B,CAAC,MAAM;MAELb,IAAI,CAACqC,WAAW,CAACT,KAAK,CAAC;IACzB;EACF;AACF;AAEe,SAASY,YAAYA,CAClCxC,IAA0B,EAC1BC,MAAoB,EAEpBmB,WAAoB,GAAG,IAAI,EAC3BlB,oBAA6B,GAAG,KAAK,EACrC;EACA,IAAIF,IAAI,CAACyC,QAAQ,CAAC,CAAC,EAAE;IACnB1C,mBAAmB,CAACC,IAAI,EAAEC,MAAM,EAAEC,oBAAoB,CAAC;EACzD,CAAC,MAAM;IACL,MAAMmB,OAAO,GAAG,IAAI,IAAIrB,IAAI,CAACG,IAAI,IAAI,CAAC,CAACH,IAAI,CAACG,IAAI,CAAC0B,EAAE;IACc;MAAA,IAAAa,KAAA,EAAAC,qBAAA;MAE/D,CAAAA,qBAAA,IAAAD,KAAA,GAAA1C,IAAI,EAAC4C,kBAAkB,YAAAD,qBAAA,GAAvBD,KAAA,CAAKE,kBAAkB,GAErBjE,OAAO,CAAC,iBAAiB,CAAC,CAACkE,QAAQ,CAACC,SAAS,CAACF,kBAAkB;IACpE;IAGA5C,IAAI,GAAGA,IAAI,CAAC4C,kBAAkB,CAAC,KAAK,CAAC;IACrC1B,aAAa,CACXlB,IAAI,EACJC,MAAM,EACNmB,WAAW,EACXlB,oBAAoB,EACpBmB,OACF,CAAC;EACH;AACF", "ignoreList": []}