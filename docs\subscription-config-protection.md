# 订阅配置保护功能

## 概述

为了确保订阅配置文件的纯净性和原始网络行为，Lvory 实现了受限映射模式，只允许注入必要的用户界面设置，而不会修改订阅的核心网络配置。

## 功能特性

### 🔒 保护的配置类型

1. **DNS配置完全保护**
   - DNS服务器列表
   - DNS规则配置
   - DNS最终服务器设置

2. **路由配置完全保护**
   - 路由规则数组
   - 路由最终出站
   - 路由接口检测设置

3. **出站配置完全保护**
   - 代理出站配置
   - 选择器和URL测试组
   - 直连和阻断出站

### ✅ 允许注入的设置

1. **日志配置**
   - 日志级别 (`log_level`)
   - 日志禁用状态 (`log_disabled`)

2. **API配置**
   - Clash API启用状态 (`clash_api_enabled`)
   - API控制器地址 (`clash_api_controller`)

3. **入站配置**
   - Mixed代理入站 (`mixed_inbound`)
   - 局域网访问设置 (`mixed_listen_address`)
   - 代理端口设置 (`mixed_port`)
   - TUN模式开关 (`tun_inbound`)

## 工作原理

### 自动检测机制

系统会自动检测配置文件类型：

```javascript
function detectSubscriptionConfig(config) {
  const hasOutbounds = Array.isArray(config.outbounds) && config.outbounds.length > 0;
  const hasProxies = Array.isArray(config.proxies) && config.proxies.length > 0;
  const hasRoute = config.route && (config.route.rules || config.route.rule_set);
  const hasDNS = config.dns && (config.dns.servers || config.dns.rules);
  
  // 如果包含多个订阅特征，认为是订阅配置
  const subscriptionFeatures = [hasOutbounds, hasProxies, hasRoute, hasDNS].filter(Boolean).length;
  return subscriptionFeatures >= 2;
}
```

### 映射模式选择

- **订阅配置**: 自动启用受限模式，只应用允许的映射规则
- **用户配置**: 使用完整模式，应用所有映射规则

## 配置示例

### 受保护的订阅配置

```json
{
  "outbounds": [
    {"type": "shadowsocks", "tag": "proxy1", "server": "*******"},
    {"type": "direct", "tag": "direct"}
  ],
  "route": {
    "rules": [
      {"domain": ["google.com"], "outbound": "proxy1"}
    ],
    "final": "proxy1"
  },
  "dns": {
    "servers": [
      {"tag": "remote", "address": "*******"}
    ],
    "final": "remote"
  }
}
```

### 处理后的配置

```json
{
  "outbounds": [
    {"type": "shadowsocks", "tag": "proxy1", "server": "*******"},
    {"type": "direct", "tag": "direct"}
  ],
  "route": {
    "rules": [
      {"domain": ["google.com"], "outbound": "proxy1"}
    ],
    "final": "proxy1"
  },
  "dns": {
    "servers": [
      {"tag": "remote", "address": "*******"}
    ],
    "final": "remote"
  },
  "inbounds": [
    {"type": "mixed", "tag": "mixed-in", "listen": "127.0.0.1", "listen_port": 7890},
    {"type": "tun", "tag": "tun-in", "address": ["**********/30"]}
  ],
  "log": {
    "level": "info",
    "output": "/path/to/log/file"
  }
}
```

## 技术实现

### 映射规则过滤

```javascript
const ALLOWED_MAPPING_RULES = [
  'log_level', 'log_disabled',
  'clash_api_enabled', 'clash_api_controller', 
  'mixed_inbound', 'mixed_listen_address', 'mixed_port',
  'tun_inbound'
];

function getAllowedMappingRules() {
  const allRules = getAllMappingRules();
  return allRules.filter(rule => ALLOWED_MAPPING_RULES.includes(rule.name));
}
```

### 受限模式映射引擎

```javascript
const restrictedEngine = new MappingEngine({ restrictedMode: true });
const result = await restrictedEngine.applyMappings(userConfig, subscriptionConfig);
```

## 使用场景

1. **订阅配置处理**: 自动保护订阅文件的原始网络配置
2. **配置文件预处理**: 在应用用户设置时保持订阅逻辑
3. **多配置管理**: 确保不同来源的配置文件保持其特定行为

## 优势

- ✅ **保持订阅纯净**: 不修改订阅提供商的网络配置
- ✅ **用户体验**: 仍然可以调整界面相关设置
- ✅ **网络稳定**: 避免因配置冲突导致的连接问题
- ✅ **自动化**: 无需用户手动选择，系统自动识别并处理
