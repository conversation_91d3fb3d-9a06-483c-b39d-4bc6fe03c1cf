#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/git/lvory/node_modules/.store/webpack@5.99.9/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/git/lvory/node_modules/.store/webpack@5.99.9/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/webpack@5.99.9/node_modules/webpack/bin/webpack.js" "$@"
else
  exec node  "$basedir/../.store/webpack@5.99.9/node_modules/webpack/bin/webpack.js" "$@"
fi
