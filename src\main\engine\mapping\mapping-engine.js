/**
 * 映射引擎
 * 负责执行用户配置到sing-box配置的映射转换
 */

const logger = require('../../../utils/logger');
const { cloneDeep, get, set, merge } = require('lodash');
const { MappingType } = require('./mapping-types');
const { getAllMappingRules, getAllowedMappingRules } = require('./mapping-definitions');

/**
 * 映射引擎类
 */
class MappingEngine {
  constructor(options = {}) {
    this.options = {
      restrictedMode: false, // 是否启用受限模式（只应用允许的规则）
      ...options
    };

    this.mappingRules = [];
    this.transformFunctions = new Map();
    this.validatorFunctions = new Map();
    this.pathCache = new Map();
    this.statistics = {
      totalMappings: 0,
      successfulMappings: 0,
      failedMappings: 0,
      executionTime: 0
    };

    this.initializeTransformFunctions();
    this.loadMappingRules();
  }

  /**
   * 初始化转换函数
   */
  initializeTransformFunctions() {
    // 代理配置转换函数
    this.transformFunctions.set('mapProxyToOutbound', (proxy) => {
      const outbound = {
        type: proxy.type,
        tag: proxy.name || proxy.tag,
        server: proxy.server,
        server_port: proxy.port
      };

      switch (proxy.type) {
        case 'shadowsocks':
          outbound.method = proxy.cipher || proxy.method;
          outbound.password = proxy.password;
          break;
        case 'vmess':
          outbound.uuid = proxy.uuid;
          outbound.security = proxy.security || 'auto';
          if (proxy.alter_id !== undefined) {
            outbound.alter_id = proxy.alter_id;
          }
          break;
        case 'trojan':
          outbound.password = proxy.password;
          break;
        case 'hysteria':
          outbound.up_mbps = proxy.up || 10;
          outbound.down_mbps = proxy.down || 50;
          outbound.auth_str = proxy.auth;
          break;
        case 'hysteria2':
          outbound.password = proxy.password;
          break;
        case 'vless':
          outbound.uuid = proxy.uuid;
          outbound.flow = proxy.flow;
          break;
      }

      // TLS配置
      if (proxy.tls) {
        outbound.tls = {
          enabled: true,
          server_name: proxy.sni || proxy.server,
          insecure: proxy.skip_cert_verify || false
        };
      }

      // 传输配置
      if (proxy.network) {
        outbound.transport = this.mapTransport(proxy);
      }

      return outbound;
    });

    // 路由规则转换函数
    this.transformFunctions.set('mapRouteRule', (rule) => {
      const routeRule = {
        outbound: rule.outbound || 'proxy'
      };

      // 域名规则
      if (rule.domain) {
        routeRule.domain = Array.isArray(rule.domain) ? rule.domain : [rule.domain];
      }
      if (rule.domain_suffix) {
        routeRule.domain_suffix = Array.isArray(rule.domain_suffix) ? rule.domain_suffix : [rule.domain_suffix];
      }
      if (rule.domain_keyword) {
        routeRule.domain_keyword = Array.isArray(rule.domain_keyword) ? rule.domain_keyword : [rule.domain_keyword];
      }

      // IP规则
      if (rule.ip_cidr) {
        routeRule.ip_cidr = Array.isArray(rule.ip_cidr) ? rule.ip_cidr : [rule.ip_cidr];
      }
      if (rule.geoip) {
        routeRule.geoip = Array.isArray(rule.geoip) ? rule.geoip : [rule.geoip];
      }

      // 端口规则
      if (rule.port) {
        routeRule.port = Array.isArray(rule.port) ? rule.port : [rule.port];
      }
      if (rule.port_range) {
        routeRule.port_range = Array.isArray(rule.port_range) ? rule.port_range : [rule.port_range];
      }

      // 协议规则
      if (rule.protocol) {
        routeRule.protocol = Array.isArray(rule.protocol) ? rule.protocol : [rule.protocol];
      }

      return routeRule;
    });

    // DNS规则转换函数
    this.transformFunctions.set('mapDNSRule', (rule) => {
      const dnsRule = {
        server: rule.server || 'google'
      };

      // 域名规则
      if (rule.domain) {
        dnsRule.domain = Array.isArray(rule.domain) ? rule.domain : [rule.domain];
      }
      if (rule.domain_suffix) {
        dnsRule.domain_suffix = Array.isArray(rule.domain_suffix) ? rule.domain_suffix : [rule.domain_suffix];
      }

      // GeoSite规则
      if (rule.geosite) {
        dnsRule.geosite = Array.isArray(rule.geosite) ? rule.geosite : [rule.geosite];
      }

      return dnsRule;
    });
  }

  /**
   * 映射传输配置
   * @param {Object} proxy 代理配置
   * @returns {Object} 传输配置
   */
  mapTransport(proxy) {
    const transport = {
      type: proxy.network
    };

    switch (proxy.network) {
      case 'ws':
        transport.path = proxy.ws_opts?.path || '/';
        if (proxy.ws_opts?.headers) {
          transport.headers = proxy.ws_opts.headers;
        }
        break;
      case 'grpc':
        transport.service_name = proxy.grpc_opts?.grpc_service_name || '';
        break;
      case 'h2':
        transport.host = proxy.h2_opts?.host || [];
        transport.path = proxy.h2_opts?.path || '/';
        break;
    }

    return transport;
  }

  /**
   * 加载映射规则
   */
  loadMappingRules() {
    try {
      if (this.options.restrictedMode) {
        this.mappingRules = getAllowedMappingRules();
        logger.info(`已加载 ${this.mappingRules.length} 个允许的映射规则（受限模式）`);
      } else {
        this.mappingRules = getAllMappingRules();
        logger.info(`已加载 ${this.mappingRules.length} 个映射规则（完整模式）`);
      }
    } catch (error) {
      logger.error(`加载映射规则失败: ${error.message}`);
      this.mappingRules = [];
    }
  }

  /**
   * 重新加载映射规则
   */
  reloadMappingRules() {
    this.pathCache.clear();
    this.loadMappingRules();
  }

  /**
   * 执行映射转换
   * @param {Object} userConfig 用户配置
   * @param {Object} baseConfig 基础配置（可选）
   * @returns {Promise<Object>} 转换结果
   */
  async applyMappings(userConfig, baseConfig = {}) {
    const startTime = Date.now();

    try {
      // 重置统计信息
      this.statistics = {
        totalMappings: 0,
        successfulMappings: 0,
        failedMappings: 0,
        executionTime: 0
      };

      // 克隆基础配置
      const targetConfig = cloneDeep(baseConfig);

      // 确保关键数组字段被初始化
      if (!Array.isArray(targetConfig.outbounds)) {
        targetConfig.outbounds = [];
      }
      if (!Array.isArray(targetConfig.inbounds)) {
        targetConfig.inbounds = [];
      }
      
      // 按优先级排序映射规则
      const sortedRules = [...this.mappingRules].sort((a, b) => b.priority - a.priority);
      
      // 执行映射
      for (const rule of sortedRules) {
        if (!rule.enabled) {
          continue;
        }

        this.statistics.totalMappings++;

        try {
          await this.applyMappingRule(userConfig, targetConfig, rule);
          this.statistics.successfulMappings++;
        } catch (error) {
          this.statistics.failedMappings++;
          logger.warn(`映射规则 "${rule.name}" 执行失败: ${error.message}`);
        }
      }

      this.statistics.executionTime = Date.now() - startTime;
      
      logger.info(`映射转换完成: ${this.statistics.successfulMappings}/${this.statistics.totalMappings} 成功, 耗时 ${this.statistics.executionTime}ms`);

      return {
        success: true,
        config: targetConfig,
        statistics: { ...this.statistics }
      };
    } catch (error) {
      this.statistics.executionTime = Date.now() - startTime;
      logger.error(`映射转换失败: ${error.message}`);
      
      return {
        success: false,
        error: error.message,
        statistics: { ...this.statistics }
      };
    }
  }

  /**
   * 应用单个映射规则
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   */
  async applyMappingRule(userConfig, targetConfig, rule) {
    // 检查依赖
    if (rule.dependencies && rule.dependencies.length > 0) {
      const dependenciesMet = rule.dependencies.every(dep => 
        this.checkDependency(targetConfig, dep)
      );
      if (!dependenciesMet) {
        return;
      }
    }

    // 获取用户配置值
    const userValue = this.getValueByPath(userConfig, rule.userPath);
    
    // 检查条件
    if (rule.conditions && rule.conditions.length > 0) {
      const conditionsMet = rule.conditions.every(condition => 
        this.evaluateCondition(condition, userValue, userConfig)
      );
      if (!conditionsMet) {
        return;
      }
    }

    // 根据映射类型执行转换
    switch (rule.type) {
      case MappingType.DIRECT:
        this.applyDirectMapping(userConfig, targetConfig, rule, userValue);
        break;
      case MappingType.CONDITIONAL:
        this.applyConditionalMapping(userConfig, targetConfig, rule, userValue);
        break;
      case MappingType.TEMPLATE:
        this.applyTemplateMapping(userConfig, targetConfig, rule, userValue);
        break;
      case MappingType.FUNCTION:
        await this.applyFunctionMapping(userConfig, targetConfig, rule, userValue);
        break;
      case MappingType.ARRAY:
        this.applyArrayMapping(userConfig, targetConfig, rule, userValue);
        break;
      case MappingType.OBJECT:
        this.applyObjectMapping(userConfig, targetConfig, rule, userValue);
        break;
      case MappingType.MERGE:
        this.applyMergeMapping(userConfig, targetConfig, rule);
        break;
      default:
        throw new Error(`不支持的映射类型: ${rule.type}`);
    }
  }

  /**
   * 应用直接映射
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   * @param {*} userValue 用户值
   */
  applyDirectMapping(userConfig, targetConfig, rule, userValue) {
    const value = userValue !== undefined ? userValue : rule.defaultValue;
    if (value !== undefined) {
      this.setValueByPath(targetConfig, rule.targetPath, value);
    }
  }

  /**
   * 应用条件映射
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   * @param {*} userValue 用户值
   */
  applyConditionalMapping(userConfig, targetConfig, rule, userValue) {
    const conditionResult = this.evaluateCondition(rule.condition, userValue, userConfig);

    if (conditionResult) {
      const value = rule.trueMapping || rule.trueValue;
      if (value !== undefined) {
        // 检查是否应该插入到数组中
        if (rule.dataType === 'array' || (rule.targetPath.includes('[') && typeof value === 'object')) {
          // 数组插入模式
          this.insertIntoArray(targetConfig, rule.targetPath, value);
        } else {
          this.setValueByPath(targetConfig, rule.targetPath, value);
        }
      }
    } else {
      const value = rule.falseMapping || rule.falseValue;
      if (value !== undefined) {
        // 检查是否是移除操作
        if (value === '__REMOVE__' || (typeof value === 'object' && value.__action === 'remove')) {
          // 移除操作
          if (rule.dataType === 'array') {
            const removeTarget = value === '__REMOVE__' ? rule.trueMapping : value;
            this.removeFromArray(targetConfig, rule.targetPath, removeTarget);
          }
        } else {
          // 正常的false值映射
          if (rule.dataType === 'array' || (rule.targetPath.includes('[') && typeof value === 'object')) {
            // 数组插入模式
            this.insertIntoArray(targetConfig, rule.targetPath, value);
          } else {
            this.setValueByPath(targetConfig, rule.targetPath, value);
          }
        }
      }
    }
  }

  /**
   * 应用模板映射
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   * @param {*} userValue 用户值
   */
  applyTemplateMapping(userConfig, targetConfig, rule, userValue) {
    let template = rule.template;
    
    // 替换变量
    if (rule.variables) {
      Object.entries(rule.variables).forEach(([key, path]) => {
        const value = this.getValueByPath(userConfig, path);
        template = template.replace(new RegExp(`{${key}}`, 'g'), value || '');
      });
    }
    
    this.setValueByPath(targetConfig, rule.targetPath, template);
  }

  /**
   * 应用函数映射
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   * @param {*} userValue 用户值
   */
  async applyFunctionMapping(userConfig, targetConfig, rule, userValue) {
    const transformFunction = this.transformFunctions.get(rule.functionName);
    if (!transformFunction) {
      throw new Error(`找不到转换函数: ${rule.functionName}`);
    }

    const parameters = { ...rule.parameters, value: userValue, userConfig, targetConfig };
    
    let result;
    if (rule.asyncFunction) {
      result = await transformFunction(userValue, parameters);
    } else {
      result = transformFunction(userValue, parameters);
    }

    if (result !== undefined) {
      this.setValueByPath(targetConfig, rule.targetPath, result);
    }
  }

  /**
   * 应用数组映射
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   * @param {*} userValue 用户值
   */
  applyArrayMapping(userConfig, targetConfig, rule, userValue) {
    if (!Array.isArray(userValue)) {
      return;
    }

    const mappedArray = userValue.map((item, index) => {
      if (rule.itemMapping) {
        if (rule.itemMapping.type === 'function') {
          const transformFunction = this.transformFunctions.get(rule.itemMapping.functionName);
          return transformFunction ? transformFunction(item, { index, userConfig, targetConfig }) : item;
        }
      }
      return item;
    });

    // 应用过滤条件
    const filteredArray = rule.filterCondition 
      ? mappedArray.filter(item => this.evaluateCondition(rule.filterCondition, item, userConfig))
      : mappedArray;

    if (rule.targetPath.includes('[') && rule.targetPath.includes(']')) {
      // 数组插入模式
      filteredArray.forEach(item => {
        this.insertIntoArray(targetConfig, rule.targetPath, item);
      });
    } else {
      this.setValueByPath(targetConfig, rule.targetPath, filteredArray);
    }
  }

  /**
   * 应用对象映射
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   * @param {*} userValue 用户值
   */
  applyObjectMapping(userConfig, targetConfig, rule, userValue) {
    if (typeof userValue !== 'object' || userValue === null) {
      return;
    }

    const mappedObject = {};

    // 应用字段映射
    if (rule.fieldMappings) {
      Object.entries(rule.fieldMappings).forEach(([sourceField, targetField]) => {
        if (userValue[sourceField] !== undefined) {
          mappedObject[targetField] = userValue[sourceField];
        }
      });
    }

    // 应用键值映射
    if (rule.keyMapping || rule.valueMapping) {
      Object.entries(userValue).forEach(([key, value]) => {
        const mappedKey = rule.keyMapping ? this.applyMapping(key, rule.keyMapping) : key;
        const mappedValue = rule.valueMapping ? this.applyMapping(value, rule.valueMapping) : value;
        mappedObject[mappedKey] = mappedValue;
      });
    }

    this.setValueByPath(targetConfig, rule.targetPath, mappedObject);
  }

  /**
   * 应用合并映射
   * @param {Object} userConfig 用户配置
   * @param {Object} targetConfig 目标配置
   * @param {Object} rule 映射规则
   */
  applyMergeMapping(userConfig, targetConfig, rule) {
    const values = rule.sourcePaths.map(path => this.getValueByPath(userConfig, path));
    
    let mergedValue;
    if (rule.mergeStrategy === 'deep') {
      mergedValue = merge({}, ...values);
    } else {
      mergedValue = Object.assign({}, ...values);
    }

    this.setValueByPath(targetConfig, rule.targetPath, mergedValue);
  }

  /**
   * 根据路径获取值
   * @param {Object} obj 对象
   * @param {string} path 路径
   * @returns {*} 值
   */
  getValueByPath(obj, path) {
    if (this.pathCache.has(path)) {
      const cachedPath = this.pathCache.get(path);
      return get(obj, cachedPath);
    }

    const normalizedPath = path.replace(/\[(\w+)=([^\]]+)\]/g, '.$1.$2');
    this.pathCache.set(path, normalizedPath);
    
    return get(obj, normalizedPath);
  }

  /**
   * 根据路径设置值
   * @param {Object} obj 对象
   * @param {string} path 路径
   * @param {*} value 值
   */
  setValueByPath(obj, path, value) {
    const normalizedPath = path.replace(/\[(\w+)=([^\]]+)\]/g, '.$1.$2');
    set(obj, normalizedPath, value);
  }

  /**
   * 插入到数组
   * @param {Object} obj 对象
   * @param {string} path 路径
   * @param {*} value 值
   */
  insertIntoArray(obj, path, value) {
    const arrayPath = path.split('[')[0];
    let array = get(obj, arrayPath);

    // 确保目标路径存在数组
    if (!Array.isArray(array)) {
      array = [];
      set(obj, arrayPath, array);
    }

    // 检查是否已存在相同tag的项目（避免重复）
    if (value && value.tag && Array.isArray(array)) {
      const existingIndex = array.findIndex(item => item && item.tag === value.tag);
      if (existingIndex !== -1) {
        // 更新现有项目
        array[existingIndex] = value;
        return;
      }
    }

    // 添加新项目
    array.push(value);
  }

  /**
   * 从数组中移除项目
   * @param {Object} obj 对象
   * @param {string} path 路径
   * @param {string|Object} identifier 标识符（tag字符串或包含tag的对象）
   */
  removeFromArray(obj, path, identifier) {
    const arrayPath = path.split('[')[0];
    let array = get(obj, arrayPath);

    if (!Array.isArray(array)) {
      return;
    }

    // 确定要移除的tag
    const targetTag = typeof identifier === 'string' ? identifier : identifier?.tag;
    if (!targetTag) {
      return;
    }

    // 移除匹配的项目
    const originalLength = array.length;
    const filteredArray = array.filter(item => item && item.tag !== targetTag);

    if (filteredArray.length !== originalLength) {
      set(obj, arrayPath, filteredArray);
      logger.info(`从 ${arrayPath} 中移除了tag为 "${targetTag}" 的项目`);
    }
  }

  /**
   * 评估条件
   * @param {string} condition 条件表达式
   * @param {*} value 值
   * @param {Object} context 上下文
   * @returns {boolean} 评估结果
   */
  evaluateCondition(condition, value, context) {
    try {
      // 简单的条件评估，实际应用中可能需要更安全的实现
      const func = new Function('value', 'context', `return ${condition}`);
      return func(value, context);
    } catch (error) {
      logger.warn(`条件评估失败: ${condition}, ${error.message}`);
      return false;
    }
  }

  /**
   * 检查依赖
   * @param {Object} config 配置对象
   * @param {string} dependency 依赖名称
   * @returns {boolean} 依赖是否满足
   */
  checkDependency(config, dependency) {
    // 简单的依赖检查，可以根据需要扩展
    return true;
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStatistics() {
    return { ...this.statistics };
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.pathCache.clear();
  }

  /**
   * 根据类别获取映射规则
   * @param {string} category 类别名称
   * @returns {Array} 映射规则数组
   */
  getMappingRulesByCategory(category) {
    return this.mappingRules.filter(rule =>
      rule.category === category ||
      rule.name.toLowerCase().includes(category.toLowerCase()) ||
      rule.description.toLowerCase().includes(category.toLowerCase())
    );
  }

  /**
   * 获取所有映射规则
   * @returns {Array} 映射规则数组
   */
  getAllMappingRules() {
    return [...this.mappingRules];
  }
}

module.exports = MappingEngine;
