#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/git/lvory/node_modules/.store/webpack-dev-server@5.2.2/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/git/lvory/node_modules/.store/webpack-dev-server@5.2.2/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/webpack-dev-server@5.2.2/node_modules/webpack-dev-server/bin/webpack-dev-server.js" "$@"
else
  exec node  "$basedir/../.store/webpack-dev-server@5.2.2/node_modules/webpack-dev-server/bin/webpack-dev-server.js" "$@"
fi
