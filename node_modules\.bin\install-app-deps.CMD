@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=%~dp0\D:\git\lvory\node_modules\.store\electron-builder@25.1.8\node_modules"
) ELSE (
  @SET "NODE_PATH=%NODE_PATH%;%~dp0\D:\git\lvory\node_modules\.store\electron-builder@25.1.8\node_modules"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.store\electron-builder@25.1.8\node_modules\electron-builder\install-app-deps.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.store\electron-builder@25.1.8\node_modules\electron-builder\install-app-deps.js" %*
)
