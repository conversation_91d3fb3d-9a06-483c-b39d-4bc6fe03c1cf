/**
 * 配置引擎单元测试
 */

const ConfigEngine = require('../src/main/engine/config-engine');
const path = require('path');
const fs = require('fs').promises;

describe('ConfigEngine', () => {
  let configEngine;
  const testConfigPath = path.join(__dirname, 'fixtures/sing-box-config.json');

  beforeEach(() => {
    configEngine = new ConfigEngine({
      enableCache: true,
      enableValidation: true,
      enableBackup: true
    });
  });

  afterEach(() => {
    if (configEngine) {
      configEngine.destroy();
    }
  });

  describe('基础功能测试', () => {
    test('应该能够创建配置引擎实例', () => {
      expect(configEngine).toBeDefined();
      expect(configEngine.parser).toBeDefined();
      expect(configEngine.validator).toBeDefined();
      expect(configEngine.mappingEngine).toBeDefined();
    });

    test('应该能够解析配置文件', async () => {
      const result = await configEngine.parseConfig(testConfigPath);
      
      expect(result.success).toBe(true);
      expect(result.config).toBeDefined();
      expect(result.config.inbounds).toBeDefined();
      expect(result.config.outbounds).toBeDefined();
      expect(result.analysis).toBeDefined();
    });

    test('应该能够验证配置', async () => {
      const parseResult = await configEngine.parseConfig(testConfigPath);
      const validation = configEngine.validateConfig(parseResult.config);
      
      expect(validation).toBeDefined();
      expect(validation.valid).toBeDefined();
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
    });
  });

  describe('用户配置映射测试', () => {
    test('应该能够应用简单的用户配置', async () => {
      const userConfig = {
        proxy: {
          type: 'shadowsocks',
          server: '1.2.3.4',
          port: 8388,
          password: 'test123',
          method: 'aes-256-gcm'
        }
      };

      const result = await configEngine.applyUserConfigMapping(userConfig);
      
      expect(result.success).toBe(true);
      expect(result.config).toBeDefined();
      expect(result.config.outbounds).toBeDefined();
      expect(result.statistics).toBeDefined();
    });

    test('应该能够应用复杂的用户配置', async () => {
      const userConfig = {
        proxy: {
          type: 'vmess',
          server: '*******',
          port: 443,
          uuid: '********-1234-1234-1234-********9abc',
          tls: true,
          ws: {
            path: '/path',
            headers: { Host: 'example.com' }
          }
        },
        routing: {
          rules: [
            {
              domain: ['google.com'],
              outbound: 'proxy'
            }
          ]
        }
      };

      const result = await configEngine.applyUserConfigMapping(userConfig);
      
      expect(result.success).toBe(true);
      expect(result.config.outbounds).toBeDefined();
      expect(result.config.route).toBeDefined();
    });
  });

  describe('配置更新和回滚测试', () => {
    test('应该能够增量更新配置', async () => {
      // 先加载基础配置
      await configEngine.injectionAPI.loadConfig(testConfigPath);
      
      const updates = {
        outbounds: [
          {
            action: 'add',
            outbound: {
              type: 'shadowsocks',
              tag: 'test-proxy',
              server: '*******',
              server_port: 8388,
              method: 'aes-256-gcm',
              password: 'testpass'
            }
          }
        ]
      };

      const result = await configEngine.updateConfig(updates);
      
      expect(result.success).toBe(true);
      expect(result.config).toBeDefined();
      
      // 检查新增的出站
      const newOutbound = result.config.outbounds.find(ob => ob.tag === 'test-proxy');
      expect(newOutbound).toBeDefined();
      expect(newOutbound.server).toBe('*******');
    });

    test('应该能够回滚配置', async () => {
      await configEngine.injectionAPI.loadConfig(testConfigPath);
      const originalConfig = configEngine.getCurrentConfig();
      
      // 进行更新
      const updates = {
        outbounds: [
          {
            action: 'add',
            outbound: {
              type: 'direct',
              tag: 'temp-direct'
            }
          }
        ]
      };
      
      await configEngine.updateConfig(updates);
      
      // 回滚
      const rollbackResult = await configEngine.rollbackConfig();
      
      expect(rollbackResult.success).toBe(true);
      
      // 验证回滚后的配置
      const currentConfig = configEngine.getCurrentConfig();
      expect(currentConfig.outbounds.length).toBe(originalConfig.outbounds.length);
    });
  });

  describe('性能和缓存测试', () => {
    test('应该能够缓存解析结果', async () => {
      // 第一次解析
      const result1 = await configEngine.parseConfig(testConfigPath);
      
      // 第二次解析（应该命中缓存）
      const result2 = await configEngine.parseConfig(testConfigPath);
      
      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      
      // 检查性能指标
      const metrics = configEngine.getPerformanceMetrics();
      expect(metrics.cacheHits).toBeGreaterThan(0);
    });

    test('应该能够处理大型配置', async () => {
      const largeConfig = generateLargeTestConfig(100, 500);
      
      const validation = configEngine.validateConfig(largeConfig);
      
      expect(validation).toBeDefined();
      expect(validation.valid).toBeDefined();
      
      // 检查性能指标
      const metrics = configEngine.getPerformanceMetrics();
      expect(metrics.totalOperations).toBeGreaterThan(0);
    });

    test('应该能够清除缓存', () => {
      configEngine.clearCache('all');
      
      const metrics = configEngine.getPerformanceMetrics();
      // 缓存清除后，缓存命中率应该重置
      expect(metrics.cacheHitRate).toBe(0);
    });
  });

  describe('错误处理测试', () => {
    test('应该能够处理无效的配置文件', async () => {
      const invalidPath = path.join(__dirname, 'fixtures/invalid-config.json');
      
      await expect(configEngine.parseConfig(invalidPath)).rejects.toThrow();
    });

    test('应该能够处理无效的用户配置', async () => {
      const invalidUserConfig = {
        proxy: {
          type: 'invalid-type',
          server: 'invalid-server'
        }
      };

      await expect(configEngine.applyUserConfigMapping(invalidUserConfig))
        .rejects.toThrow();
    });

    test('应该能够验证配置错误', () => {
      const invalidConfig = {
        inbounds: [
          {
            type: 'mixed',
            // 缺少必需的 tag 字段
            listen_port: 'invalid-port' // 无效的端口类型
          }
        ]
      };

      const validation = configEngine.validateConfig(invalidConfig);
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('事件系统测试', () => {
    test('应该能够监听配置解析事件', async () => {
      let eventFired = false;
      
      configEngine.on('configParsed', (data) => {
        eventFired = true;
        expect(data.path).toBeDefined();
        expect(data.result).toBeDefined();
      });

      await configEngine.parseConfig(testConfigPath);
      
      expect(eventFired).toBe(true);
    });

    test('应该能够监听映射应用事件', async () => {
      let eventFired = false;
      
      configEngine.on('mappingApplied', (data) => {
        eventFired = true;
        expect(data.userConfig).toBeDefined();
        expect(data.result).toBeDefined();
      });

      const userConfig = {
        proxy: {
          type: 'shadowsocks',
          server: '1.1.1.1',
          port: 8388
        }
      };

      await configEngine.applyUserConfigMapping(userConfig);
      
      expect(eventFired).toBe(true);
    });
  });

  describe('映射规则测试', () => {
    test('应该能够获取映射规则', () => {
      const allRules = configEngine.getMappingRules();
      expect(Array.isArray(allRules)).toBe(true);
      expect(allRules.length).toBeGreaterThan(0);
      
      const basicRules = configEngine.getMappingRules('basic');
      expect(Array.isArray(basicRules)).toBe(true);
    });

    test('应该能够验证映射规则', () => {
      const validation = configEngine.validateMappingRules();
      expect(validation).toBeDefined();
      expect(validation.valid).toBeDefined();
    });
  });

  describe('配置分析测试', () => {
    test('应该能够分析配置', async () => {
      const parseResult = await configEngine.parseConfig(testConfigPath);
      const analysis = configEngine.getConfigAnalysis(parseResult.config);
      
      expect(analysis).toBeDefined();
      expect(analysis.protocolStats).toBeDefined();
      expect(analysis.portUsage).toBeDefined();
      expect(analysis.summary).toBeDefined();
    });
  });

  describe('备份管理测试', () => {
    test('应该能够获取备份列表', async () => {
      await configEngine.injectionAPI.loadConfig(testConfigPath);
      
      const backups = configEngine.getBackupList();
      expect(Array.isArray(backups)).toBe(true);
      expect(backups.length).toBeGreaterThan(0);
    });

    test('应该能够创建和删除备份', async () => {
      await configEngine.injectionAPI.loadConfig(testConfigPath);
      
      const backupId = await configEngine.injectionAPI.createBackup('test');
      expect(backupId).toBeDefined();
      
      const deleted = configEngine.injectionAPI.deleteBackup(backupId);
      expect(deleted).toBe(true);
    });
  });
});

/**
 * 生成大型测试配置
 * @param {number} outboundCount 出站数量
 * @param {number} ruleCount 规则数量
 * @returns {Object} 测试配置
 */
function generateLargeTestConfig(outboundCount, ruleCount) {
  const config = {
    inbounds: [
      {
        type: 'mixed',
        tag: 'mixed-in',
        listen: '127.0.0.1',
        listen_port: 7890
      }
    ],
    outbounds: [],
    route: {
      rules: [],
      final: 'direct'
    }
  };

  // 生成出站
  for (let i = 0; i < outboundCount; i++) {
    config.outbounds.push({
      type: 'shadowsocks',
      tag: `proxy-${i}`,
      server: `192.168.1.${(i % 254) + 1}`,
      server_port: 8388 + (i % 100),
      method: 'aes-256-gcm',
      password: `password-${i}`
    });
  }

  // 添加直连出站
  config.outbounds.push({
    type: 'direct',
    tag: 'direct'
  });

  // 生成路由规则
  for (let i = 0; i < ruleCount; i++) {
    config.route.rules.push({
      domain: [`domain-${i}.com`],
      outbound: `proxy-${i % outboundCount}`
    });
  }

  return config;
}
