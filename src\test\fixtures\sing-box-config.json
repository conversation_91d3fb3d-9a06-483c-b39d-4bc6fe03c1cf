{"log": {"level": "info", "timestamp": true}, "dns": {"servers": [{"tag": "google", "address": "*******", "address_resolver": "local", "strategy": "prefer_ipv4", "detour": "direct"}, {"tag": "local", "address": "***************", "detour": "direct"}], "rules": [{"domain": ["google.com", "youtube.com", "gmail.com"], "server": "google"}, {"domain_suffix": [".cn", ".中国"], "server": "local"}], "final": "google", "strategy": "prefer_ipv4", "disable_cache": false, "disable_expire": false}, "inbounds": [{"type": "mixed", "tag": "mixed-in", "listen": "127.0.0.1", "listen_port": 7890, "sniff": true, "sniff_override_destination": true, "domain_strategy": "prefer_ipv4"}, {"type": "tun", "tag": "tun-in", "interface_name": "tun0", "inet4_address": "**********/30", "mtu": 9000, "auto_route": true, "strict_route": true, "sniff": true, "sniff_override_destination": true, "domain_strategy": "prefer_ipv4"}], "outbounds": [{"type": "shadowsocks", "tag": "proxy-ss", "server": "*******", "server_port": 8388, "method": "aes-256-gcm", "password": "test123456"}, {"type": "vmess", "tag": "proxy-vmess", "server": "*******", "server_port": 443, "uuid": "12345678-1234-1234-1234-123456789abc", "security": "auto", "alter_id": 0, "global_padding": false, "authenticated_length": true, "tls": {"enabled": true, "server_name": "example.com", "insecure": false, "alpn": ["h2", "http/1.1"]}, "transport": {"type": "ws", "path": "/path", "headers": {"Host": "example.com"}}}, {"type": "trojan", "tag": "proxy-trojan", "server": "**********", "server_port": 443, "password": "trojan123456", "tls": {"enabled": true, "server_name": "trojan.example.com", "insecure": false, "alpn": ["h2", "http/1.1"]}}, {"type": "hysteria2", "tag": "proxy-hy2", "server": "***********", "server_port": 443, "password": "hysteria123456", "tls": {"enabled": true, "server_name": "hy2.example.com", "insecure": false, "alpn": ["h3"]}}, {"type": "selector", "tag": "proxy", "outbounds": ["proxy-ss", "proxy-vmess", "proxy-trojan", "proxy-hy2"], "default": "proxy-ss"}, {"type": "urltest", "tag": "auto", "outbounds": ["proxy-ss", "proxy-vmess", "proxy-trojan", "proxy-hy2"], "url": "http://www.gstatic.com/generate_204", "interval": "1m", "tolerance": 50}, {"type": "direct", "tag": "direct"}, {"type": "block", "tag": "block"}, {"type": "dns", "tag": "dns-out"}], "route": {"geoip": {"path": "geoip.db", "download_url": "https://github.com/SagerNet/sing-geoip/releases/latest/download/geoip.db", "download_detour": "direct"}, "geosite": {"path": "geosite.db", "download_url": "https://github.com/SagerNet/sing-geosite/releases/latest/download/geosite.db", "download_detour": "direct"}, "rules": [{"protocol": "dns", "outbound": "dns-out"}, {"clash_mode": "direct", "outbound": "direct"}, {"clash_mode": "global", "outbound": "proxy"}, {"domain": ["clash.razord.top", "yacd.haishan.me"], "outbound": "direct"}, {"geosite": "private", "outbound": "direct"}, {"geosite": "cn", "outbound": "direct"}, {"geoip": "private", "outbound": "direct"}, {"geoip": "cn", "outbound": "direct"}, {"domain_suffix": [".cn", ".中国"], "outbound": "direct"}, {"geosite": "google", "outbound": "proxy"}, {"geosite": "youtube", "outbound": "proxy"}, {"geosite": "github", "outbound": "proxy"}, {"geosite": "telegram", "outbound": "proxy"}, {"geosite": "twitter", "outbound": "proxy"}, {"geosite": "facebook", "outbound": "proxy"}, {"domain_keyword": ["google", "youtube", "facebook", "twitter", "instagram", "telegram"], "outbound": "proxy"}, {"domain_suffix": [".google.com", ".youtube.com", ".googlevideo.com", ".googleusercontent.com", ".googleapis.com", ".gstatic.com"], "outbound": "proxy"}], "final": "proxy", "auto_detect_interface": true}, "experimental": {"clash_api": {"external_controller": "127.0.0.1:9090", "external_ui": "ui", "secret": "", "external_ui_download_url": "https://github.com/MetaCubeX/Yacd-meta/archive/gh-pages.zip", "external_ui_download_detour": "direct", "default_mode": "rule"}, "cache_file": {"enabled": true, "path": "cache.db", "cache_id": "my_profile2", "store_fakeip": false}}}