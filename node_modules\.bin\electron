#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/git/lvory/node_modules/.store/electron@37.1.0/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/git/lvory/node_modules/.store/electron@37.1.0/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/electron@37.1.0/node_modules/electron/cli.js" "$@"
else
  exec node  "$basedir/../.store/electron@37.1.0/node_modules/electron/cli.js" "$@"
fi
