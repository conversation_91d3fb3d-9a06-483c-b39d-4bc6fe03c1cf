/**
 * 映射规则定义
 * 定义用户配置到sing-box配置的映射规则
 */

const {
  MappingType,
  DataType,
  MappingPriority,
  MappingRuleFactory
} = require('./mapping-types');

/**
 * 基础配置映射规则
 */
const basicMappingRules = [
  // 日志配置映射
  MappingRuleFactory.create(MappingType.DIRECT, {
    name: 'log_level',
    description: '日志级别映射',
    userPath: 'settings.log_level',
    targetPath: 'log.level',
    dataType: DataType.STRING,
    defaultValue: 'info',
    priority: MappingPriority.NORMAL
  }),

  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'log_disabled',
    description: '禁用日志映射',
    userPath: 'settings.log_disabled',
    targetPath: 'log.disabled',
    dataType: DataType.BOOLEAN,
    condition: 'value === true',
    trueValue: true,
    falseValue: false,
    priority: MappingPriority.NORMAL
  }),

  // 实验性功能映射
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'clash_api_enabled',
    description: 'Clash API启用映射',
    userPath: 'settings.clash_api.enabled',
    targetPath: 'experimental.clash_api',
    dataType: DataType.BOOLEAN,
    condition: 'value === true',
    trueMapping: {
      external_controller: '127.0.0.1:9090',
      secret: ''
    },
    falseValue: undefined,
    priority: MappingPriority.HIGH
  }),

  MappingRuleFactory.create(MappingType.TEMPLATE, {
    name: 'clash_api_controller',
    description: 'Clash API控制器地址映射',
    userPath: 'settings.clash_api',
    targetPath: 'experimental.clash_api.external_controller',
    template: '{host}:{port}',
    variables: {
      host: 'settings.clash_api.host',
      port: 'settings.clash_api.port'
    },
    conditions: ['settings.clash_api.enabled === true'],
    priority: MappingPriority.HIGH
  })
];

/**
 * 入站配置映射规则
 */
const inboundMappingRules = [
  // Mixed入站映射
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'mixed_inbound',
    description: 'Mixed入站配置映射',
    userPath: 'proxy.mixed.enabled',
    targetPath: 'inbounds',
    dataType: DataType.ARRAY,
    condition: 'value === true',
    trueMapping: {
      type: 'mixed',
      tag: 'mixed-in',
      listen: '127.0.0.1',
      listen_port: 7890
    },
    priority: MappingPriority.HIGH
  }),

  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'mixed_listen_address',
    description: 'Mixed监听地址映射',
    userPath: 'settings.allow_lan',
    targetPath: 'inbounds.[tag=mixed-in].listen',
    dataType: DataType.STRING,
    condition: 'value === true',
    trueValue: '0.0.0.0',
    falseValue: '127.0.0.1',
    dependencies: ['mixed_inbound'],
    priority: MappingPriority.NORMAL
  }),

  MappingRuleFactory.create(MappingType.DIRECT, {
    name: 'mixed_port',
    description: 'Mixed端口映射',
    userPath: 'proxy.mixed.port',
    targetPath: 'inbounds.[tag=mixed-in].listen_port',
    dataType: DataType.NUMBER,
    defaultValue: 7890,
    dependencies: ['mixed_inbound'],
    priority: MappingPriority.NORMAL
  }),

  // TUN入站映射
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'tun_inbound',
    description: 'TUN入站配置映射',
    userPath: 'settings.tun_mode',
    targetPath: 'inbounds',
    dataType: DataType.ARRAY,
    condition: 'value === true',
    trueMapping: {
      type: 'tun',
      tag: 'tun-in',
      address: ['**********/30', 'fdfe:dcba:9876::1/126'],
      auto_route: true,
      strict_route: true,
      stack: 'system'
    },
    falseMapping: '__REMOVE__',
    priority: MappingPriority.HIGH
  }),

  MappingRuleFactory.create(MappingType.ARRAY, {
    name: 'tun_addresses',
    description: 'TUN地址映射',
    userPath: 'tun.addresses',
    targetPath: 'inbounds.[tag=tun-in].address',
    dataType: DataType.ARRAY,
    dependencies: ['tun_inbound'],
    priority: MappingPriority.NORMAL
  }),

  MappingRuleFactory.create(MappingType.DIRECT, {
    name: 'tun_auto_route',
    description: 'TUN自动路由映射',
    userPath: 'tun.auto_route',
    targetPath: 'inbounds.[tag=tun-in].auto_route',
    dataType: DataType.BOOLEAN,
    defaultValue: true,
    dependencies: ['tun_inbound'],
    priority: MappingPriority.NORMAL
  })
];

/**
 * 出站配置映射规则
 */
const outboundMappingRules = [
  // 直连出站
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'direct_outbound',
    description: '直连出站配置',
    userPath: 'outbounds.direct',
    targetPath: 'outbounds',
    dataType: DataType.ARRAY,
    condition: 'value !== false',
    trueMapping: {
      type: 'direct',
      tag: 'direct'
    },
    priority: MappingPriority.CRITICAL
  }),

  // 阻断出站
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'block_outbound',
    description: '阻断出站配置',
    userPath: 'outbounds.block',
    targetPath: 'outbounds',
    dataType: DataType.ARRAY,
    condition: 'value !== false',
    trueMapping: {
      type: 'block',
      tag: 'block'
    },
    priority: MappingPriority.CRITICAL
  }),

  // DNS出站
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'dns_outbound',
    description: 'DNS出站配置',
    userPath: 'outbounds.dns',
    targetPath: 'outbounds',
    dataType: DataType.ARRAY,
    condition: 'value !== false',
    trueMapping: {
      type: 'dns',
      tag: 'dns-out'
    },
    priority: MappingPriority.HIGH
  }),

  // 代理出站数组映射
  MappingRuleFactory.create(MappingType.ARRAY, {
    name: 'proxy_outbounds',
    description: '代理出站配置映射',
    userPath: 'proxies',
    targetPath: 'outbounds',
    dataType: DataType.ARRAY,
    itemMapping: {
      type: 'function',
      functionName: 'mapProxyToOutbound'
    },
    priority: MappingPriority.HIGH
  }),

  // 选择器出站
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'selector_outbound',
    description: '选择器出站配置',
    userPath: 'groups.selector.enabled',
    targetPath: 'outbounds',
    condition: 'value === true',
    trueMapping: {
      type: 'selector',
      tag: 'proxy',
      outbounds: []
    },
    priority: MappingPriority.HIGH
  }),

  // URL测试出站
  MappingRuleFactory.create(MappingType.CONDITIONAL, {
    name: 'urltest_outbound',
    description: 'URL测试出站配置',
    userPath: 'groups.urltest.enabled',
    targetPath: 'outbounds',
    condition: 'value === true',
    trueMapping: {
      type: 'urltest',
      tag: 'auto',
      outbounds: [],
      url: 'http://www.gstatic.com/generate_204',
      interval: '1m'
    },
    priority: MappingPriority.HIGH
  })
];

/**
 * 路由配置映射规则
 */
const routeMappingRules = [
  // 基础路由配置
  MappingRuleFactory.create(MappingType.DIRECT, {
    name: 'route_final',
    description: '路由最终出站',
    userPath: 'route.final',
    targetPath: 'route.final',
    dataType: DataType.STRING,
    defaultValue: 'proxy',
    priority: MappingPriority.HIGH
  }),

  MappingRuleFactory.create(MappingType.DIRECT, {
    name: 'route_auto_detect_interface',
    description: '路由自动检测接口',
    userPath: 'route.auto_detect_interface',
    targetPath: 'route.auto_detect_interface',
    dataType: DataType.BOOLEAN,
    defaultValue: true,
    priority: MappingPriority.NORMAL
  }),

  // 路由规则数组映射
  MappingRuleFactory.create(MappingType.ARRAY, {
    name: 'route_rules',
    description: '路由规则映射',
    userPath: 'route.rules',
    targetPath: 'route.rules',
    dataType: DataType.ARRAY,
    itemMapping: {
      type: 'function',
      functionName: 'mapRouteRule'
    },
    priority: MappingPriority.HIGH
  })
];

/**
 * DNS配置映射规则
 */
const dnsMappingRules = [
  // DNS服务器配置
  MappingRuleFactory.create(MappingType.ARRAY, {
    name: 'dns_servers',
    description: 'DNS服务器映射',
    userPath: 'dns.servers',
    targetPath: 'dns.servers',
    dataType: DataType.ARRAY,
    defaultValue: [
      {
        tag: 'google',
        address: '*******'
      },
      {
        tag: 'cloudflare',
        address: '*******'
      }
    ],
    priority: MappingPriority.NORMAL
  }),

  // DNS规则映射
  MappingRuleFactory.create(MappingType.ARRAY, {
    name: 'dns_rules',
    description: 'DNS规则映射',
    userPath: 'dns.rules',
    targetPath: 'dns.rules',
    dataType: DataType.ARRAY,
    itemMapping: {
      type: 'function',
      functionName: 'mapDNSRule'
    },
    priority: MappingPriority.NORMAL
  }),

  // DNS最终服务器
  MappingRuleFactory.create(MappingType.DIRECT, {
    name: 'dns_final',
    description: 'DNS最终服务器',
    userPath: 'dns.final',
    targetPath: 'dns.final',
    dataType: DataType.STRING,
    defaultValue: 'google',
    priority: MappingPriority.NORMAL
  })
];

/**
 * 允许注入的映射规则名称列表
 * 只有这些规则会被应用到订阅配置文件中，确保订阅配置的纯净性
 */
const ALLOWED_MAPPING_RULES = [
  // 日志配置 - 允许（用户界面设置，不影响网络行为）
  'log_level',
  'log_disabled',

  // API配置 - 允许（用户界面设置，不影响网络行为）
  'clash_api_enabled',
  'clash_api_controller',

  // 入站配置 - 允许（用户界面设置，不影响订阅的网络路由）
  'mixed_inbound',      // Mixed代理入站
  'mixed_listen_address', // 局域网访问设置
  'mixed_port',         // 代理端口设置
  'tun_inbound',        // TUN模式开关

  // === 以下规则被禁用以保护订阅配置 ===

  // TUN详细配置 - 禁用（保持订阅或系统默认）
  // 'tun_addresses',     // TUN地址配置
  // 'tun_auto_route',    // TUN自动路由

  // 出站配置 - 禁用（完全保持订阅原始配置）
  // 'direct_outbound',   // 直连出站
  // 'block_outbound',    // 阻断出站
  // 'dns_outbound',      // DNS出站
  // 'proxy_outbounds',   // 代理出站数组
  // 'selector_outbound', // 选择器出站
  // 'urltest_outbound',  // URL测试出站

  // 路由配置 - 禁用（完全保持订阅原始路由逻辑）
  // 'route_final',              // 路由最终出站
  // 'route_auto_detect_interface', // 路由自动检测接口
  // 'route_rules',              // 路由规则数组

  // DNS配置 - 禁用（完全保持订阅原始DNS设置）
  // 'dns_servers',       // DNS服务器配置
  // 'dns_rules',         // DNS规则配置
  // 'dns_final'          // DNS最终服务器
];

/**
 * 获取所有映射规则
 * @returns {Array} 映射规则数组
 */
function getAllMappingRules() {
  return [
    ...basicMappingRules,
    ...inboundMappingRules,
    ...outboundMappingRules,
    ...routeMappingRules,
    ...dnsMappingRules
  ].sort((a, b) => b.priority - a.priority);
}

/**
 * 获取允许的映射规则（用于订阅配置注入）
 * @returns {Array} 允许的映射规则数组
 */
function getAllowedMappingRules() {
  const allRules = getAllMappingRules();
  return allRules.filter(rule => ALLOWED_MAPPING_RULES.includes(rule.name))
    .sort((a, b) => b.priority - a.priority);
}

/**
 * 根据类别获取映射规则
 * @param {string} category 类别
 * @returns {Array} 映射规则数组
 */
function getMappingRulesByCategory(category) {
  const categories = {
    basic: basicMappingRules,
    inbound: inboundMappingRules,
    outbound: outboundMappingRules,
    route: routeMappingRules,
    dns: dnsMappingRules
  };

  return categories[category] || [];
}

/**
 * 根据名称获取映射规则
 * @param {string} name 规则名称
 * @returns {Object|null} 映射规则
 */
function getMappingRuleByName(name) {
  const allRules = getAllMappingRules();
  return allRules.find(rule => rule.name === name) || null;
}

/**
 * 根据用户路径获取映射规则
 * @param {string} userPath 用户路径
 * @returns {Array} 映射规则数组
 */
function getMappingRulesByUserPath(userPath) {
  const allRules = getAllMappingRules();
  return allRules.filter(rule => rule.userPath === userPath);
}

/**
 * 根据目标路径获取映射规则
 * @param {string} targetPath 目标路径
 * @returns {Array} 映射规则数组
 */
function getMappingRulesByTargetPath(targetPath) {
  const allRules = getAllMappingRules();
  return allRules.filter(rule => rule.targetPath === targetPath);
}

/**
 * 验证所有映射规则
 * @returns {Object} 验证结果
 */
function validateAllMappingRules() {
  const allRules = getAllMappingRules();
  const results = {
    valid: true,
    totalRules: allRules.length,
    validRules: 0,
    invalidRules: 0,
    errors: []
  };

  allRules.forEach((rule, index) => {
    const validation = rule.validate();
    if (validation.valid) {
      results.validRules++;
    } else {
      results.invalidRules++;
      results.errors.push({
        ruleIndex: index,
        ruleName: rule.name,
        errors: validation.errors
      });
    }
  });

  results.valid = results.invalidRules === 0;
  return results;
}

module.exports = {
  basicMappingRules,
  inboundMappingRules,
  outboundMappingRules,
  routeMappingRules,
  dnsMappingRules,
  ALLOWED_MAPPING_RULES,
  getAllMappingRules,
  getAllowedMappingRules,
  getMappingRulesByCategory,
  getMappingRuleByName,
  getMappingRulesByUserPath,
  getMappingRulesByTargetPath,
  validateAllMappingRules
};
