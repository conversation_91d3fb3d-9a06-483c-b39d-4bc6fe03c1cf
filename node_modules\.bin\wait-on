#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/git/lvory/node_modules/.store/wait-on@8.0.3/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/git/lvory/node_modules/.store/wait-on@8.0.3/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/wait-on@8.0.3/node_modules/wait-on/bin/wait-on" "$@"
else
  exec node  "$basedir/../.store/wait-on@8.0.3/node_modules/wait-on/bin/wait-on" "$@"
fi
