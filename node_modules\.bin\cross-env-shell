#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/git/lvory/node_modules/.store/cross-env@7.0.3/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/git/lvory/node_modules/.store/cross-env@7.0.3/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/cross-env@7.0.3/node_modules/cross-env/src/bin/cross-env-shell.js" "$@"
else
  exec node  "$basedir/../.store/cross-env@7.0.3/node_modules/cross-env/src/bin/cross-env-shell.js" "$@"
fi
